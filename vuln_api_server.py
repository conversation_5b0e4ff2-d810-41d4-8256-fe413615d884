#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
绿盟漏洞API服务器
提供HTTP API接口获取原理漏洞的详细信息
"""

from flask import Flask, jsonify, request
from flask_cors import CORS
import logging
from datetime import datetime, timedelta
import json
import os
import threading
import time
import math

from login_manager import GreenLeagueLogin
from task_manager import TaskManager
from vuln_manager import VulnManager

class VulnAPIServer:
    """漏洞API服务器类"""

    def __init__(self, host="************", server_port=5000):
        """
        初始化API服务器
        参数:
            host: 绿盟服务器地址
            server_port: API服务器端口
        """
        self.host = host
        self.server_port = server_port
        self.app = Flask(__name__)
        CORS(self.app)  # 启用跨域支持

        # 配置日志
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)

        # 初始化管理器
        self.login_manager = None
        self.task_manager = None
        self.vuln_manager = None
        self.session = None
        self.base_url = None

        # 登录状态
        self.is_logged_in = False
        self.last_login_time = None
        self.login_timeout = 600  # 登录超时时间（秒），默认10 min

        # 数据缓存配置
        self.cache_file = "vuln_data_cache.json"
        self.cache_update_interval = 1800  # 30分钟 = 1800秒
        self.last_cache_update = None
        self.cache_data = None
        self.cache_lock = threading.Lock()
        self.cache_thread = None
        self.stop_cache_thread = False

        # 注册路由
        self._register_routes()

        # 启动时自动登录
        self._auto_login()

        # 启动缓存更新线程
        self._start_cache_thread()
    
    def _register_routes(self):
        """注册API路由"""

        @self.app.route('/', methods=['GET'])
        def index():
            """API首页"""
            # 检查当前登录状态
            login_status = self._check_login()

            # 计算登录时长
            login_duration = None
            if self.last_login_time:
                duration_seconds = (datetime.now() - self.last_login_time).total_seconds()
                login_duration = f"{duration_seconds:.0f}秒"

            # 获取缓存状态
            cache_status = "未初始化"
            cache_info = {}
            cache_data = self._get_cached_data()
            if cache_data:
                cache_status = "已缓存"
                cache_info = {
                    'total_vulns': cache_data.get('total_count', 0),
                    'task_count': cache_data.get('task_count', 0),
                    'last_update': cache_data.get('last_update'),
                    'update_interval': f"{self.cache_update_interval}秒"
                }

                # 检查缓存是否需要更新
                if self._should_update_cache():
                    cache_status = "需要更新"

            return jsonify({
                'message': '绿盟原理漏洞API服务器',
                'version': '2.0.0',
                'status': '已登录' if login_status else '未登录',
                'login_time': self.last_login_time.isoformat() if self.last_login_time else None,
                'login_duration': login_duration,
                'login_timeout': f"{self.login_timeout}秒",
                'target_host': self.host,
                'cache_status': cache_status,
                'cache_info': cache_info,
                'endpoints': {
                    'GET /': '服务器信息和状态',
                    'GET /principle-vulns?page=1&size=20': '获取所有已完成任务的原理扫描漏洞（支持分页）'
                },
                'features': [
                    '自动登录和会话管理',
                    '登录状态过期自动重新登录',
                    '专注原理扫描漏洞分析',
                    '详细的漏洞统计信息',
                    '数据缓存机制（每30分钟自动更新）',
                    '分页查询支持'
                ]
            })
        
        @self.app.route('/principle-vulns', methods=['GET'])
        def get_all_principle_vulns():
            """获取所有已完成任务的原理扫描漏洞（支持分页）"""
            try:
                # 获取分页参数
                page = request.args.get('page', 1, type=int)
                size = request.args.get('size', 20, type=int)

                # 参数验证
                if page < 1:
                    page = 1
                if size < 1 or size > 1000:  # 限制最大页面大小
                    size = 20

                # 从缓存获取数据
                cache_data = self._get_cached_data()
                if not cache_data:
                    return jsonify({
                        'success': False,
                        'message': '数据缓存未就绪，请稍后重试',
                        'data': {
                            'vulns': [],
                            'total_count': 0,
                            'page': page,
                            'size': size,
                            'total_pages': 0
                        }
                    }), 503

                # 获取所有漏洞数据
                all_vulns = cache_data.get('vulns', [])
                total_count = len(all_vulns)

                # 计算分页
                total_pages = math.ceil(total_count / size) if total_count > 0 else 1
                start_index = (page - 1) * size
                end_index = start_index + size

                # 获取当前页数据
                page_vulns = all_vulns[start_index:end_index]

                return jsonify({
                    'success': True,
                    'message': f'成功获取第 {page} 页漏洞数据',
                    'data': {
                        'vulns': page_vulns,
                        'total_count': total_count,
                        'page': page,
                        'size': size,
                        'total_pages': total_pages,
                        'has_next': page < total_pages,
                        'has_prev': page > 1,
                        'task_count': cache_data.get('task_count', 0),
                        'task_ids': cache_data.get('task_ids', []),
                        'statistics': cache_data.get('statistics', {}),
                        'last_update': cache_data.get('last_update'),
                        'cache_status': 'active'
                    }
                })

            except Exception as e:
                self.logger.error(f"获取原理扫描漏洞异常: {str(e)}")
                return jsonify({
                    'success': False,
                    'message': f'获取原理扫描漏洞异常: {str(e)}',
                    'data': {
                        'vulns': [],
                        'total_count': 0,
                        'page': page,
                        'size': size,
                        'total_pages': 0
                    }
                }), 500
        

    def _get_cached_data(self):
        """获取缓存数据"""
        with self.cache_lock:
            if self.cache_data is not None:
                return self.cache_data

            # 尝试从文件加载缓存
            if os.path.exists(self.cache_file):
                try:
                    with open(self.cache_file, 'r', encoding='utf-8') as f:
                        self.cache_data = json.load(f)
                        self.last_cache_update = datetime.fromisoformat(
                            self.cache_data.get('last_update', datetime.now().isoformat())
                        )
                        self.logger.info(f"从文件加载缓存数据成功，共 {len(self.cache_data.get('vulns', []))} 个漏洞")
                        return self.cache_data
                except Exception as e:
                    self.logger.error(f"加载缓存文件失败: {str(e)}")

            return None

    def _update_cache_data(self):
        """更新缓存数据"""
        try:
            self.logger.info("开始更新漏洞数据缓存...")

            # 检查登录状态
            if not self._check_login():
                self.logger.error("登录失败，无法更新缓存")
                return False

            # 获取已完成任务
            success, completed_tasks = self.task_manager.get_completed_tasks()
            if not success:
                self.logger.error("获取已完成任务失败")
                return False

            if not completed_tasks:
                self.logger.warning("没有找到有漏洞的任务")
                return False

            # 提取任务ID列表
            task_ids = [task['task_id'] for task in completed_tasks]

            # 分析多个任务的漏洞
            analysis_result = self.vuln_manager.analyze_multiple_tasks_vulns(task_ids)

            # 构建缓存数据
            cache_data = {
                'task_count': len(task_ids),
                'task_ids': task_ids,
                'vulns': analysis_result['all_vulns'],
                'total_count': len(analysis_result['all_vulns']),
                'statistics': analysis_result['statistics'],
                'task_results': analysis_result['task_results'],
                'last_update': datetime.now().isoformat()
            }

            # 保存到内存和文件
            with self.cache_lock:
                self.cache_data = cache_data
                self.last_cache_update = datetime.now()

                # 保存到文件
                with open(self.cache_file, 'w', encoding='utf-8') as f:
                    json.dump(cache_data, f, ensure_ascii=False, indent=2)

            self.logger.info(f"缓存更新成功，共 {cache_data['total_count']} 个漏洞")
            return True

        except Exception as e:
            self.logger.error(f"更新缓存数据异常: {str(e)}")
            return False

    def _should_update_cache(self):
        """检查是否需要更新缓存"""
        if self.last_cache_update is None:
            return True

        time_diff = (datetime.now() - self.last_cache_update).total_seconds()
        return time_diff >= self.cache_update_interval

    def _cache_update_worker(self):
        """缓存更新工作线程"""
        self.logger.info("缓存更新线程启动")

        # 首次启动时立即更新缓存
        self._update_cache_data()

        while not self.stop_cache_thread:
            try:
                if self._should_update_cache():
                    self._update_cache_data()

                # 每分钟检查一次是否需要更新
                time.sleep(60)

            except Exception as e:
                self.logger.error(f"缓存更新线程异常: {str(e)}")
                time.sleep(60)

        self.logger.info("缓存更新线程停止")

    def _start_cache_thread(self):
        """启动缓存更新线程"""
        if self.cache_thread is None or not self.cache_thread.is_alive():
            self.stop_cache_thread = False
            self.cache_thread = threading.Thread(target=self._cache_update_worker, daemon=True)
            self.cache_thread.start()
            self.logger.info("缓存更新线程已启动")

    def _stop_cache_thread(self):
        """停止缓存更新线程"""
        self.stop_cache_thread = True
        if self.cache_thread and self.cache_thread.is_alive():
            self.cache_thread.join(timeout=5)
            self.logger.info("缓存更新线程已停止")

    def _check_login(self):
        """检查登录状态，如果过期则自动重新登录"""
        # 检查基本登录状态
        if not self.is_logged_in or self.session is None:
            self.logger.warning("未登录，尝试自动登录...")
            return self._auto_login()

        # 检查登录是否超时
        if self.last_login_time:
            current_time = datetime.now()
            time_diff = (current_time - self.last_login_time).total_seconds()

            if time_diff > self.login_timeout:
                self.logger.warning(f"登录已超时 ({time_diff:.0f}秒)，尝试重新登录...")
                return self._auto_login()

        # 测试会话是否仍然有效
        if not self._test_session():
            self.logger.warning("会话已失效，尝试重新登录...")
            return self._auto_login()

        return True

    def _test_session(self):
        """测试当前会话是否有效"""
        try:
            if not self.task_manager:
                return False

            # 尝试获取任务列表来测试会话
            success, _ = self.task_manager.get_task_list(page=1, page_size=1)
            return success

        except Exception as e:
            self.logger.error(f"测试会话失败: {str(e)}")
            return False

    def _auto_login(self):
        """自动登录绿盟系统，返回登录是否成功"""
        try:
            self.logger.info("正在自动登录绿盟系统...")

            # 重置登录状态
            self.is_logged_in = False
            self.last_login_time = None

            # 初始化登录管理器
            self.login_manager = GreenLeagueLogin(host=self.host)

            # 执行自动登录
            success, _ = self.login_manager.auto_login()

            if success:
                # 初始化其他管理器
                self.session = self.login_manager.get_session()
                self.base_url = self.login_manager.get_base_url()
                self.task_manager = TaskManager(self.session, self.base_url)
                self.vuln_manager = VulnManager(self.session, self.base_url)

                self.is_logged_in = True
                self.last_login_time = datetime.now()

                self.logger.info("✓ 自动登录绿盟系统成功")
                return True
            else:
                self.logger.error("✗ 自动登录绿盟系统失败")
                return False

        except Exception as e:
            self.logger.error(f"自动登录异常: {str(e)}")
            return False

    def run(self, debug=False):
        """启动API服务器"""
        self.logger.info(f"启动原理漏洞API服务器，端口: {self.server_port}")
        self.logger.info(f"绿盟服务器地址: {self.host}")
        self.logger.info("API接口:")
        self.logger.info(f"  GET  http://localhost:{self.server_port}/")
        self.logger.info(f"  GET  http://localhost:{self.server_port}/principle-vulns")

        self.app.run(host='0.0.0.0', port=self.server_port, debug=debug)

def main():
    """主函数 - 启动参数已写死"""
    # 写死的启动参数
    host = '************'  # 绿盟服务器地址
    port = 45678          # API服务器端口
    debug = False         # 调试模式

    # 创建并启动服务器
    server = VulnAPIServer(host=host, server_port=port)
    server.run(debug=debug)

if __name__ == '__main__':
    main()
