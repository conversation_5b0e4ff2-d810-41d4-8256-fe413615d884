#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试API修改后的功能
"""

import requests
import json
import time

def test_api_server():
    """测试API服务器功能"""
    base_url = "http://localhost:45678"
    
    print("=" * 60)
    print("测试绿盟漏洞API服务器")
    print("=" * 60)
    
    # 1. 测试首页
    print("\n1. 测试首页...")
    try:
        response = requests.get(f"{base_url}/")
        if response.status_code == 200:
            data = response.json()
            print("✓ 首页访问成功")
            print(f"  版本: {data.get('version')}")
            print(f"  状态: {data.get('status')}")
            print(f"  缓存状态: {data.get('cache_status')}")
            if data.get('cache_info'):
                cache_info = data['cache_info']
                print(f"  缓存信息:")
                print(f"    总漏洞数: {cache_info.get('total_vulns', 0)}")
                print(f"    任务数: {cache_info.get('task_count', 0)}")
                print(f"    最后更新: {cache_info.get('last_update', '未知')}")
        else:
            print(f"✗ 首页访问失败，状态码: {response.status_code}")
    except Exception as e:
        print(f"✗ 首页访问异常: {str(e)}")
    
    # 2. 测试分页查询
    print("\n2. 测试分页查询...")
    test_cases = [
        {"page": 1, "size": 5},
        {"page": 2, "size": 10},
        {"page": 1, "size": 50}
    ]
    
    for case in test_cases:
        try:
            params = {"page": case["page"], "size": case["size"]}
            response = requests.get(f"{base_url}/principle-vulns", params=params)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    result_data = data.get('data', {})
                    print(f"✓ 第{case['page']}页查询成功 (每页{case['size']}条)")
                    print(f"  返回漏洞数: {len(result_data.get('vulns', []))}")
                    print(f"  总漏洞数: {result_data.get('total_count', 0)}")
                    print(f"  总页数: {result_data.get('total_pages', 0)}")
                    print(f"  有下一页: {result_data.get('has_next', False)}")
                    print(f"  有上一页: {result_data.get('has_prev', False)}")
                else:
                    print(f"✗ 第{case['page']}页查询失败: {data.get('message')}")
            else:
                print(f"✗ 第{case['page']}页查询失败，状态码: {response.status_code}")
        except Exception as e:
            print(f"✗ 第{case['page']}页查询异常: {str(e)}")
    
    # 3. 测试手动刷新缓存
    print("\n3. 测试手动刷新缓存...")
    try:
        response = requests.post(f"{base_url}/refresh-cache")
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                result_data = data.get('data', {})
                print("✓ 缓存刷新成功")
                print(f"  总漏洞数: {result_data.get('total_vulns', 0)}")
                print(f"  任务数: {result_data.get('task_count', 0)}")
                print(f"  最后更新: {result_data.get('last_update', '未知')}")
            else:
                print(f"✗ 缓存刷新失败: {data.get('message')}")
        else:
            print(f"✗ 缓存刷新失败，状态码: {response.status_code}")
    except Exception as e:
        print(f"✗ 缓存刷新异常: {str(e)}")
    
    # 4. 测试参数边界情况
    print("\n4. 测试参数边界情况...")
    edge_cases = [
        {"page": 0, "size": 10},  # 页码为0
        {"page": 1, "size": 0},   # 每页大小为0
        {"page": -1, "size": 20}, # 负数页码
        {"page": 1, "size": 2000}, # 超大页面大小
        {"page": "abc", "size": 10}, # 非数字页码
    ]
    
    for case in edge_cases:
        try:
            params = {"page": case["page"], "size": case["size"]}
            response = requests.get(f"{base_url}/principle-vulns", params=params)
            
            if response.status_code == 200:
                data = response.json()
                result_data = data.get('data', {})
                print(f"✓ 边界测试通过: page={case['page']}, size={case['size']}")
                print(f"  实际页码: {result_data.get('page')}")
                print(f"  实际大小: {result_data.get('size')}")
            else:
                print(f"✗ 边界测试失败: page={case['page']}, size={case['size']}, 状态码: {response.status_code}")
        except Exception as e:
            print(f"✗ 边界测试异常: page={case['page']}, size={case['size']}, 错误: {str(e)}")

if __name__ == "__main__":
    print("请确保API服务器已启动在 http://localhost:45678")
    print("按回车键开始测试...")
    input()
    
    test_api_server()
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)
